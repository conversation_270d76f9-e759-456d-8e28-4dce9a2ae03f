
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/7/29 17:43:25。
      节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\code\\fault_detect\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:03.21
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/code/fault_detect/build/CMakeFiles/4.0.0-rc4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/7/29 17:43:30。
      节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\code\\fault_detect\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:01.02
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/code/fault_detect/build/CMakeFiles/4.0.0-rc4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-3a0anf"
      binary: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-3a0anf"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-3a0anf'
        
        Run Build Command(s): F:/vs2019-vc16/MSBuild/Current/Bin/MSBuild.exe cmTC_d1444.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/7/29 17:43:32。
        节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3a0anf\\cmTC_d1444.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_d1444.dir\\Debug\\”。
          正在创建目录“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3a0anf\\Debug\\”。
          正在创建目录“cmTC_d1444.dir\\Debug\\cmTC_d1444.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_d1444.dir\\Debug\\cmTC_d1444.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d1444.dir\\Debug\\\\" /Fd"cmTC_d1444.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\software\\cmake\\install\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CMakeCCompilerABI.c
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d1444.dir\\Debug\\\\" /Fd"cmTC_d1444.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\software\\cmake\\install\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
        Link:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3a0anf\\Debug\\cmTC_d1444.exe" /INCREMENTAL /ILK:"cmTC_d1444.dir\\Debug\\cmTC_d1444.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-3a0anf/Debug/cmTC_d1444.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-3a0anf/Debug/cmTC_d1444.lib" /MACHINE:X64  /machine:x64 cmTC_d1444.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_d1444.vcxproj -> D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3a0anf\\Debug\\cmTC_d1444.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_d1444.dir\\Debug\\cmTC_d1444.tlog\\unsuccessfulbuild”。
          正在对“cmTC_d1444.dir\\Debug\\cmTC_d1444.tlog\\cmTC_d1444.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3a0anf\\cmTC_d1444.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.39
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': F:/vs2019-vc16/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "F:/vs2019-vc16/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-103l7d"
      binary: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-103l7d"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-103l7d'
        
        Run Build Command(s): F:/vs2019-vc16/MSBuild/Current/Bin/MSBuild.exe cmTC_5ee52.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/7/29 17:43:34。
        节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-103l7d\\cmTC_5ee52.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_5ee52.dir\\Debug\\”。
          正在创建目录“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-103l7d\\Debug\\”。
          正在创建目录“cmTC_5ee52.dir\\Debug\\cmTC_5ee52.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_5ee52.dir\\Debug\\cmTC_5ee52.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_5ee52.dir\\Debug\\\\" /Fd"cmTC_5ee52.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "D:\\software\\cmake\\install\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CMakeCXXCompilerABI.cpp
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_5ee52.dir\\Debug\\\\" /Fd"cmTC_5ee52.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TP /errorReport:queue "D:\\software\\cmake\\install\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-103l7d\\Debug\\cmTC_5ee52.exe" /INCREMENTAL /ILK:"cmTC_5ee52.dir\\Debug\\cmTC_5ee52.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-103l7d/Debug/cmTC_5ee52.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-103l7d/Debug/cmTC_5ee52.lib" /MACHINE:X64  /machine:x64 cmTC_5ee52.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_5ee52.vcxproj -> D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-103l7d\\Debug\\cmTC_5ee52.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_5ee52.dir\\Debug\\cmTC_5ee52.tlog\\unsuccessfulbuild”。
          正在对“cmTC_5ee52.dir\\Debug\\cmTC_5ee52.tlog\\cmTC_5ee52.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-103l7d\\cmTC_5ee52.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.35
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': F:/vs2019-vc16/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "F:/vs2019-vc16/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:24 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-ax7x5p"
      binary: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-ax7x5p"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-ax7x5p'
        
        Run Build Command(s): F:/vs2019-vc16/MSBuild/Current/Bin/MSBuild.exe cmTC_7a954.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/7/29 17:43:36。
        节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\cmTC_7a954.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_7a954.dir\\Debug\\”。
          正在创建目录“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\Debug\\”。
          正在创建目录“cmTC_7a954.dir\\Debug\\cmTC_7a954.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_7a954.dir\\Debug\\cmTC_7a954.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7a954.dir\\Debug\\\\" /Fd"cmTC_7a954.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          src.c
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7a954.dir\\Debug\\\\" /Fd"cmTC_7a954.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\src.c"
        D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\src.c(1,10): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\cmTC_7a954.vcxproj]
        已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\cmTC_7a954.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\cmTC_7a954.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\src.c(1,10): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ax7x5p\\cmTC_7a954.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.66
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:24 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-cz8w6c"
      binary: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-cz8w6c"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-cz8w6c'
        
        Run Build Command(s): F:/vs2019-vc16/MSBuild/Current/Bin/MSBuild.exe cmTC_41bf7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/7/29 17:43:37。
        节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\cmTC_41bf7.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_41bf7.dir\\Debug\\”。
          正在创建目录“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\Debug\\”。
          正在创建目录“cmTC_41bf7.dir\\Debug\\cmTC_41bf7.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_41bf7.dir\\Debug\\cmTC_41bf7.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_41bf7.dir\\Debug\\\\" /Fd"cmTC_41bf7.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\CheckFunctionExists.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CheckFunctionExists.c
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_41bf7.dir\\Debug\\\\" /Fd"cmTC_41bf7.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\CheckFunctionExists.c"
        Link:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\Debug\\cmTC_41bf7.exe" /INCREMENTAL /ILK:"cmTC_41bf7.dir\\Debug\\cmTC_41bf7.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-cz8w6c/Debug/cmTC_41bf7.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-cz8w6c/Debug/cmTC_41bf7.lib" /MACHINE:X64  /machine:x64 cmTC_41bf7.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\cmTC_41bf7.vcxproj]
        已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\cmTC_41bf7.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\cmTC_41bf7.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-cz8w6c\\cmTC_41bf7.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.61
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/software/cmake/install/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "D:/software/cmake/install/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:24 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-v3yh3w"
      binary: "D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-v3yh3w"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-v3yh3w'
        
        Run Build Command(s): F:/vs2019-vc16/MSBuild/Current/Bin/MSBuild.exe cmTC_a317f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/7/29 17:43:38。
        节点 1 上的项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\cmTC_a317f.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_a317f.dir\\Debug\\”。
          正在创建目录“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\Debug\\”。
          正在创建目录“cmTC_a317f.dir\\Debug\\cmTC_a317f.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_a317f.dir\\Debug\\cmTC_a317f.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a317f.dir\\Debug\\\\" /Fd"cmTC_a317f.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\CheckFunctionExists.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CheckFunctionExists.c
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a317f.dir\\Debug\\\\" /Fd"cmTC_a317f.dir\\Debug\\vc142.pdb" /external:W3 /Gd /TC /errorReport:queue "D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\CheckFunctionExists.c"
        Link:
          F:\\vs2019-vc16\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\Debug\\cmTC_a317f.exe" /INCREMENTAL /ILK:"cmTC_a317f.dir\\Debug\\cmTC_a317f.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-v3yh3w/Debug/cmTC_a317f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/code/fault_detect/build/CMakeFiles/CMakeScratch/TryCompile-v3yh3w/Debug/cmTC_a317f.lib" /MACHINE:X64  /machine:x64 cmTC_a317f.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\cmTC_a317f.vcxproj]
        已完成生成项目“D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\cmTC_a317f.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\cmTC_a317f.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [D:\\code\\fault_detect\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v3yh3w\\cmTC_a317f.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.56
        
      exitCode: 1
...
