﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8975D24E-BF1A-318D-8168-16002100FC76}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>FaultDetect</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\fault_detect\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">FaultDetect.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">FaultDetect</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\fault_detect\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">FaultDetect.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">FaultDetect</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\code\fault_detect\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">FaultDetect.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">FaultDetect</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\code\fault_detect\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">FaultDetect.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">FaultDetect</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/code/fault_detect/build/Debug/FaultDetect.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/code/fault_detect/build/bin/Debug/FaultDetect.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/code/fault_detect/build/Release/FaultDetect.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/code/fault_detect/build/bin/Release/FaultDetect.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/code/fault_detect/build/MinSizeRel/FaultDetect.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/code/fault_detect/build/bin/MinSizeRel/FaultDetect.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>ws2_32.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/code/fault_detect/build/RelWithDebInfo/FaultDetect.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/code/fault_detect/build/bin/RelWithDebInfo/FaultDetect.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\code\fault_detect\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/code/fault_detect/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-file D:/code/fault_detect/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/code/fault_detect/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-file D:/code/fault_detect/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/code/fault_detect/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-file D:/code/fault_detect/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/code/fault_detect/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-file D:/code/fault_detect/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\code\fault_detect\src\common_simple.cpp" />
    <ClCompile Include="D:\code\fault_detect\src\test_simple.cpp" />
    <ClInclude Include="D:\code\fault_detect\include\camera_manager.h" />
    <ClInclude Include="D:\code\fault_detect\include\common.h" />
    <ClInclude Include="D:\code\fault_detect\include\common_simple.h" />
    <ClInclude Include="D:\code\fault_detect\include\damage_detector.h" />
    <ClInclude Include="D:\code\fault_detect\include\image_processor.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\code\fault_detect\build\ZERO_CHECK.vcxproj">
      <Project>{4299C755-07D7-38BB-99E2-92B943761249}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>