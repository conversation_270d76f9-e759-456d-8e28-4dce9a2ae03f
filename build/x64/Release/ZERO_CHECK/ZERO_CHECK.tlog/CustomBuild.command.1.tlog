^D:\CODE\FAULT_DETECT\BUILD\CMAKEFILES\9E488A929017AE5F73997CB02EFF0FEC\GENERATE.STAMP.RULE
setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/fault_detect/build/FaultDetect.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
