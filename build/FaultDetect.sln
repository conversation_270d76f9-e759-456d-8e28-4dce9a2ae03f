﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{632D702A-3EE8-3A20-8452-B3A24C37D777}"
	ProjectSection(ProjectDependencies) = postProject
		{8975D24E-BF1A-318D-8168-16002100FC76} = {8975D24E-BF1A-318D-8168-16002100FC76}
		{4299C755-07D7-38BB-99E2-92B943761249} = {4299C755-07D7-38BB-99E2-92B943761249}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "FaultDetect", "FaultDetect.vcxproj", "{8975D24E-BF1A-318D-8168-16002100FC76}"
	ProjectSection(ProjectDependencies) = postProject
		{4299C755-07D7-38BB-99E2-92B943761249} = {4299C755-07D7-38BB-99E2-92B943761249}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{4299C755-07D7-38BB-99E2-92B943761249}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{632D702A-3EE8-3A20-8452-B3A24C37D777}.Debug|x64.ActiveCfg = Debug|x64
		{632D702A-3EE8-3A20-8452-B3A24C37D777}.Release|x64.ActiveCfg = Release|x64
		{632D702A-3EE8-3A20-8452-B3A24C37D777}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{632D702A-3EE8-3A20-8452-B3A24C37D777}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.Debug|x64.ActiveCfg = Debug|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.Debug|x64.Build.0 = Debug|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.Release|x64.ActiveCfg = Release|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.Release|x64.Build.0 = Release|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8975D24E-BF1A-318D-8168-16002100FC76}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.Debug|x64.ActiveCfg = Debug|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.Debug|x64.Build.0 = Debug|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.Release|x64.ActiveCfg = Release|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.Release|x64.Build.0 = Release|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4299C755-07D7-38BB-99E2-92B943761249}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6DD4DAD9-4770-3BA0-8D85-AF20A0C0C321}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
