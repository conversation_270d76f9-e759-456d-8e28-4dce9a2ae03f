﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4299C755-07D7-38BB-99E2-92B943761249}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\code\fault_detect\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\code\fault_detect\build\CMakeFiles\9e488a929017ae5f73997cb02eff0fec\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/fault_detect/build/FaultDetect.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\fault_detect\CMakeLists.txt;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/fault_detect/build/FaultDetect.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\fault_detect\CMakeLists.txt;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/fault_detect/build/FaultDetect.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\code\fault_detect\CMakeLists.txt;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\software\cmake\install\bin\cmake.exe -SD:/code/fault_detect -BD:/code/fault_detect/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/code/fault_detect/build/FaultDetect.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\code\fault_detect\CMakeLists.txt;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\code\fault_detect\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeRCInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckIncludeFile.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\CheckLibraryExists.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Compiler\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindPackageMessage.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\FindThreads.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Linker\MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\Windows.cmake;D:\software\cmake\install\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig-version.cmake;E:\opencvnew\opencv-4.3.0\build\install\OpenCVConfig.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\code\fault_detect\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>