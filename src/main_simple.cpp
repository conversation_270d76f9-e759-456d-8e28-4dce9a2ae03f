#include "../include/common_simple.h"
#include <iostream>
#include <signal.h>

// 全局变量用于信号处理
std::atomic<bool> g_running(true);

// 信号处理函数
void signalHandler(int signal) {
    Utils::logInfo("接收到信号 " + std::to_string(signal) + "，准备退出...");
    g_running = false;
}

// 测试基础功能
void testBasicFunctions() {
    Utils::logInfo("=== 开始测试基础功能 ===");
    
    // 测试时间函数
    Utils::logInfo("当前时间: " + Utils::getCurrentTimeString());
    
    // 测试损伤类型转换
    Utils::logInfo("测试损伤类型转换:");
    for (int i = 0; i <= 7; ++i) {
        DamageType type = static_cast<DamageType>(i);
        Utils::logInfo("  类型 " + std::to_string(i) + ": " + Utils::damageTypeToString(type));
    }
    
    // 测试单位转换
    Utils::logInfo("测试单位转换:");
    double pixels = 100.0;
    double mm = Utils::pixelToMm(pixels);
    double backToPixels = Utils::mmToPixel(mm);
    Utils::logInfo("  " + std::to_string(pixels) + " pixels = " + 
                   std::to_string(mm) + " mm = " + std::to_string(backToPixels) + " pixels");
    
    // 测试距离计算
    Utils::logInfo("测试距离计算:");
    SimplePoint p1(0, 0);
    SimplePoint p2(3, 4);
    double distance = Utils::calculateDistance(p1, p2);
    Utils::logInfo("  点(" + std::to_string(p1.x) + "," + std::to_string(p1.y) + 
                   ") 到 点(" + std::to_string(p2.x) + "," + std::to_string(p2.y) + 
                   ") 的距离: " + std::to_string(distance));
    
    // 测试目录创建
    Utils::logInfo("测试目录创建:");
    if (Utils::createDirectory("output/test")) {
        Utils::logInfo("  测试目录创建成功: output/test");
    }
    
    // 测试图像保存 (简化版本)
    Utils::logInfo("测试图像保存:");
    SimpleImage testImage;
    testImage.width = 640;
    testImage.height = 480;
    testImage.channels = 3;
    testImage.data.resize(testImage.width * testImage.height * testImage.channels, 128);
    
    if (Utils::saveImage(testImage, "output/test/test_image.jpg")) {
        Utils::logInfo("  测试图像保存成功");
    }
    
    Utils::logInfo("=== 基础功能测试完成 ===");
}

// 测试数据结构
void testDataStructures() {
    Utils::logInfo("=== 开始测试数据结构 ===");
    
    // 测试损伤信息结构
    Utils::logInfo("测试损伤信息结构:");
    DamageInfo damage;
    damage.type = DamageType::CRACK;
    damage.boundingBox = SimpleRect(100, 100, 50, 200);
    damage.center = SimplePoint(125, 200);
    damage.area = 1000.0;
    damage.length = 200.0;
    damage.width = 5.0;
    damage.confidence = 0.85;
    damage.description = "检测到的裂缝";
    
    Utils::logInfo("  损伤类型: " + Utils::damageTypeToString(damage.type));
    Utils::logInfo("  边界框: (" + std::to_string(damage.boundingBox.x) + "," + 
                   std::to_string(damage.boundingBox.y) + "," + 
                   std::to_string(damage.boundingBox.width) + "," + 
                   std::to_string(damage.boundingBox.height) + ")");
    Utils::logInfo("  中心点: (" + std::to_string(damage.center.x) + "," + 
                   std::to_string(damage.center.y) + ")");
    Utils::logInfo("  面积: " + std::to_string(damage.area) + " 像素");
    Utils::logInfo("  长度: " + std::to_string(damage.length) + " 像素");
    Utils::logInfo("  宽度: " + std::to_string(damage.width) + " 像素");
    Utils::logInfo("  置信度: " + std::to_string(damage.confidence));
    Utils::logInfo("  描述: " + damage.description);
    
    // 测试检测结果结构
    Utils::logInfo("测试检测结果结构:");
    DetectionResult result;
    result.cameraId = 0;
    result.processingTime = 45.6;
    result.isValid = true;
    result.damages.push_back(damage);
    
    Utils::logInfo("  摄像头ID: " + std::to_string(result.cameraId));
    Utils::logInfo("  处理时间: " + std::to_string(result.processingTime) + " ms");
    Utils::logInfo("  结果有效: " + std::string(result.isValid ? "是" : "否"));
    Utils::logInfo("  检测到损伤数量: " + std::to_string(result.damages.size()));
    
    // 测试摄像头信息结构
    Utils::logInfo("测试摄像头信息结构:");
    CameraInfo cameraInfo;
    cameraInfo.id = 0;
    cameraInfo.devicePath = "/dev/video0";
    cameraInfo.status = CameraStatus::CAPTURING;
    cameraInfo.width = Config::CAMERA_WIDTH;
    cameraInfo.height = Config::CAMERA_HEIGHT;
    cameraInfo.fps = Config::TARGET_FPS;
    
    Utils::logInfo("  摄像头ID: " + std::to_string(cameraInfo.id));
    Utils::logInfo("  设备路径: " + cameraInfo.devicePath);
    Utils::logInfo("  状态: " + std::to_string(static_cast<int>(cameraInfo.status)));
    Utils::logInfo("  分辨率: " + std::to_string(cameraInfo.width) + "x" + 
                   std::to_string(cameraInfo.height));
    Utils::logInfo("  帧率: " + std::to_string(cameraInfo.fps) + " fps");
    
    Utils::logInfo("=== 数据结构测试完成 ===");
}

// 测试线程安全队列
void testThreadSafeQueue() {
    Utils::logInfo("=== 开始测试线程安全队列 ===");
    
    ThreadSafeQueue<int> queue(5);
    
    // 测试基本操作
    Utils::logInfo("测试队列基本操作:");
    Utils::logInfo("  初始队列大小: " + std::to_string(queue.size()));
    Utils::logInfo("  队列是否为空: " + std::string(queue.empty() ? "是" : "否"));
    
    // 添加元素
    for (int i = 1; i <= 3; ++i) {
        queue.push(i);
        Utils::logInfo("  添加元素 " + std::to_string(i) + ", 队列大小: " + std::to_string(queue.size()));
    }
    
    // 取出元素
    int item;
    while (queue.pop(item, 100)) {  // 100ms超时
        Utils::logInfo("  取出元素: " + std::to_string(item) + ", 剩余大小: " + std::to_string(queue.size()));
    }
    
    Utils::logInfo("=== 线程安全队列测试完成 ===");
}

// 显示系统配置
void displaySystemConfig() {
    Utils::logInfo("=== 系统配置信息 ===");
    Utils::logInfo("摄像头数量: " + std::to_string(Config::CAMERA_COUNT));
    Utils::logInfo("图像分辨率: " + std::to_string(Config::CAMERA_WIDTH) + "x" + 
                   std::to_string(Config::CAMERA_HEIGHT));
    Utils::logInfo("目标帧率: " + std::to_string(Config::TARGET_FPS) + " fps");
    Utils::logInfo("最小裂缝宽度: " + std::to_string(Config::MIN_CRACK_WIDTH) + " mm");
    Utils::logInfo("最小损伤尺寸: " + std::to_string(Config::MIN_DAMAGE_SIZE) + " mm");
    Utils::logInfo("最大队列长度: " + std::to_string(Config::MAX_QUEUE_SIZE));
    Utils::logInfo("处理超时时间: " + std::to_string(Config::PROCESS_TIMEOUT) + " ms");
    Utils::logInfo("RTSP端口: " + std::to_string(Config::RTSP_PORT));
    Utils::logInfo("RTSP路径: " + Config::RTSP_PATH);
    Utils::logInfo("========================");
}

int main() {
    Utils::logInfo("=== 拉吊索缺损识别系统 (简化版本) ===");
    Utils::logInfo("版本: 1.0.0");
    Utils::logInfo("编译时间: " + std::string(__DATE__) + " " + std::string(__TIME__));
    
    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    try {
        // 显示系统配置
        displaySystemConfig();
        
        // 运行各项测试
        testBasicFunctions();
        
        if (g_running) {
            testDataStructures();
        }
        
        if (g_running) {
            testThreadSafeQueue();
        }
        
        Utils::logInfo("=== 所有测试完成 ===");
        Utils::logInfo("系统运行正常，基础功能验证通过");
        Utils::logInfo("要启用完整功能，请安装OpenCV并重新编译");
        
        // 等待用户中断
        Utils::logInfo("按 Ctrl+C 退出系统");
        while (g_running) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        
    } catch (const std::exception& e) {
        Utils::logError("系统运行异常: " + std::string(e.what()));
        return -1;
    }
    
    Utils::logInfo("系统正常退出");
    return 0;
}
