#include "../../include/camera_manager.h"
#include "../../include/common.h"
#include <algorithm>

CameraManager::CameraManager() : isRunning_(false) {
    cameras_.resize(Config::CAMERA_COUNT);
    cameraInfos_.resize(Config::CAMERA_COUNT);
    lastFrameTime_.resize(Config::CAMERA_COUNT);
    actualFPS_.resize(Config::CAMERA_COUNT, 0.0);
    
    // 初始化摄像头信息
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        cameraInfos_[i].id = i;
        cameraInfos_[i].status = CameraStatus::DISCONNECTED;
        cameraInfos_[i].width = Config::CAMERA_WIDTH;
        cameraInfos_[i].height = Config::CAMERA_HEIGHT;
        cameraInfos_[i].fps = 0.0;
    }
}

CameraManager::~CameraManager() {
    stopCapture();
    releaseAllCameras();
}

bool CameraManager::initialize() {
    Utils::logInfo("开始初始化摄像头管理器...");
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 检测可用摄像头
    std::vector<int> availableCameras = detectAvailableCameras();
    if (availableCameras.empty()) {
        Utils::logError("未检测到可用摄像头");
        return false;
    }
    
    Utils::logInfo("检测到 " + std::to_string(availableCameras.size()) + " 个可用摄像头");
    
    // 初始化每个摄像头
    bool allSuccess = true;
    for (int i = 0; i < Config::CAMERA_COUNT && i < availableCameras.size(); ++i) {
        if (!initializeCamera(i)) {
            Utils::logError("初始化摄像头 " + std::to_string(i) + " 失败");
            allSuccess = false;
        }
    }
    
    if (!allSuccess) {
        Utils::logWarning("部分摄像头初始化失败，系统将使用可用的摄像头");
    }
    
    Utils::logInfo("摄像头管理器初始化完成");
    return true;
}

bool CameraManager::startCapture() {
    Utils::logInfo("启动摄像头采集...");
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (isRunning_) {
        Utils::logWarning("摄像头采集已在运行");
        return true;
    }
    
    // 启动所有可用摄像头
    bool anyStarted = false;
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        if (cameraInfos_[i].status == CameraStatus::CONNECTED) {
            if (cameras_[i] && cameras_[i]->isOpened()) {
                cameraInfos_[i].status = CameraStatus::CAPTURING;
                lastFrameTime_[i] = std::chrono::steady_clock::now();
                anyStarted = true;
                Utils::logInfo("摄像头 " + std::to_string(i) + " 开始采集");
            }
        }
    }
    
    if (anyStarted) {
        isRunning_ = true;
        Utils::logInfo("摄像头采集启动成功");
        return true;
    } else {
        Utils::logError("没有可用的摄像头启动采集");
        return false;
    }
}

void CameraManager::stopCapture() {
    Utils::logInfo("停止摄像头采集...");
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!isRunning_) {
        return;
    }
    
    isRunning_ = false;
    
    // 停止所有摄像头采集
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        if (cameraInfos_[i].status == CameraStatus::CAPTURING) {
            cameraInfos_[i].status = CameraStatus::CONNECTED;
            Utils::logInfo("摄像头 " + std::to_string(i) + " 停止采集");
        }
    }
    
    Utils::logInfo("摄像头采集已停止");
}

bool CameraManager::getFrame(int cameraId, cv::Mat& frame) {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        Utils::logError("无效的摄像头ID: " + std::to_string(cameraId));
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!isRunning_ || cameraInfos_[cameraId].status != CameraStatus::CAPTURING) {
        return false;
    }
    
    if (!cameras_[cameraId] || !cameras_[cameraId]->isOpened()) {
        updateCameraStatus(cameraId, CameraStatus::ERROR, "摄像头未打开");
        return false;
    }
    
    bool success = cameras_[cameraId]->read(frame);
    if (success && !frame.empty()) {
        updateFPS(cameraId);
        return true;
    } else {
        updateCameraStatus(cameraId, CameraStatus::ERROR, "读取帧失败");
        return false;
    }
}

bool CameraManager::getAllFrames(std::vector<cv::Mat>& frames) {
    frames.clear();
    frames.resize(Config::CAMERA_COUNT);
    
    bool anySuccess = false;
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        if (getFrame(i, frames[i])) {
            anySuccess = true;
        }
    }
    
    return anySuccess;
}

CameraInfo CameraManager::getCameraInfo(int cameraId) const {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        CameraInfo info;
        info.id = -1;
        info.errorMsg = "无效的摄像头ID";
        return info;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return cameraInfos_[cameraId];
}

std::vector<CameraInfo> CameraManager::getAllCameraInfo() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return cameraInfos_;
}

bool CameraManager::isCameraAvailable(int cameraId) const {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return cameraInfos_[cameraId].status == CameraStatus::CONNECTED ||
           cameraInfos_[cameraId].status == CameraStatus::CAPTURING;
}

double CameraManager::getActualFPS(int cameraId) const {
    if (cameraId < 0 || cameraId >= Config::CAMERA_COUNT) {
        return 0.0;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return actualFPS_[cameraId];
}

// 私有方法实现
bool CameraManager::initializeCamera(int cameraId) {
    Utils::logInfo("初始化摄像头 " + std::to_string(cameraId) + "...");
    
    try {
        cameras_[cameraId] = std::make_unique<cv::VideoCapture>(cameraId);
        
        if (!cameras_[cameraId]->isOpened()) {
            updateCameraStatus(cameraId, CameraStatus::ERROR, "无法打开摄像头");
            return false;
        }
        
        // 设置摄像头参数
        cameras_[cameraId]->set(cv::CAP_PROP_FRAME_WIDTH, Config::CAMERA_WIDTH);
        cameras_[cameraId]->set(cv::CAP_PROP_FRAME_HEIGHT, Config::CAMERA_HEIGHT);
        cameras_[cameraId]->set(cv::CAP_PROP_FPS, Config::TARGET_FPS);
        
        // 验证设置
        int actualWidth = static_cast<int>(cameras_[cameraId]->get(cv::CAP_PROP_FRAME_WIDTH));
        int actualHeight = static_cast<int>(cameras_[cameraId]->get(cv::CAP_PROP_FRAME_HEIGHT));
        double actualFPS = cameras_[cameraId]->get(cv::CAP_PROP_FPS);
        
        cameraInfos_[cameraId].width = actualWidth;
        cameraInfos_[cameraId].height = actualHeight;
        cameraInfos_[cameraId].fps = actualFPS;
        
        // 测试采集
        if (!testCameraCapture(cameraId)) {
            updateCameraStatus(cameraId, CameraStatus::ERROR, "摄像头采集测试失败");
            return false;
        }
        
        updateCameraStatus(cameraId, CameraStatus::CONNECTED);
        Utils::logInfo("摄像头 " + std::to_string(cameraId) + " 初始化成功 (" + 
                      std::to_string(actualWidth) + "x" + std::to_string(actualHeight) + 
                      "@" + std::to_string(actualFPS) + "fps)");
        return true;
        
    } catch (const std::exception& e) {
        updateCameraStatus(cameraId, CameraStatus::ERROR, "初始化异常: " + std::string(e.what()));
        return false;
    }
}

void CameraManager::updateCameraStatus(int cameraId, CameraStatus status, const std::string& errorMsg) {
    cameraInfos_[cameraId].status = status;
    cameraInfos_[cameraId].errorMsg = errorMsg;
}

void CameraManager::updateFPS(int cameraId) {
    auto now = std::chrono::steady_clock::now();
    if (lastFrameTime_[cameraId].time_since_epoch().count() > 0) {
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - lastFrameTime_[cameraId]).count();
        if (duration > 0) {
            actualFPS_[cameraId] = 1000.0 / duration;
        }
    }
    lastFrameTime_[cameraId] = now;
}

std::vector<int> CameraManager::detectAvailableCameras() {
    std::vector<int> availableCameras;
    
    // 尝试打开摄像头0-7
    for (int i = 0; i < 8; ++i) {
        cv::VideoCapture cap(i);
        if (cap.isOpened()) {
            availableCameras.push_back(i);
            cap.release();
        }
    }
    
    return availableCameras;
}

bool CameraManager::testCameraCapture(int cameraId) {
    if (!cameras_[cameraId] || !cameras_[cameraId]->isOpened()) {
        return false;
    }
    
    cv::Mat testFrame;
    bool success = cameras_[cameraId]->read(testFrame);
    return success && !testFrame.empty();
}

void CameraManager::releaseCamera(int cameraId) {
    if (cameras_[cameraId]) {
        cameras_[cameraId]->release();
        cameras_[cameraId].reset();
    }
    updateCameraStatus(cameraId, CameraStatus::DISCONNECTED);
}

void CameraManager::releaseAllCameras() {
    for (int i = 0; i < Config::CAMERA_COUNT; ++i) {
        releaseCamera(i);
    }
}
