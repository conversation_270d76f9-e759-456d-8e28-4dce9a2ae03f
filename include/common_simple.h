#ifndef COMMON_SIMPLE_H
#define COMMON_SIMPLE_H

#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>
#include <iostream>
#include <iomanip>

// 系统配置常量
namespace Config {
    // 摄像头配置
    const int CAMERA_COUNT = 4;           // 摄像头数量
    const int CAMERA_WIDTH = 640;         // 图像宽度
    const int CAMERA_HEIGHT = 480;        // 图像高度
    const int TARGET_FPS = 15;            // 目标帧率
    
    // 检测精度要求
    const double MIN_CRACK_WIDTH = 0.1;   // 最小裂缝宽度(mm)
    const double MIN_DAMAGE_SIZE = 10.0;  // 最小块状损伤尺寸(mm)
    
    // 性能参数
    const int MAX_QUEUE_SIZE = 30;        // 最大队列长度
    const int PROCESS_TIMEOUT = 500;      // 处理超时时间(ms)
    
    // RTSP配置
    const int RTSP_PORT = 8554;           // RTSP端口
    const std::string RTSP_PATH = "/live"; // RTSP路径
}

// 损伤类型枚举
enum class DamageType {
    NONE = 0,           // 无损伤
    CRACK = 1,          // 裂缝
    WEAR = 2,           // 磨损
    SCRATCH = 3,        // 刮伤
    PIT = 4,            // 凹坑
    BULGE = 5,          // 鼓包
    AGING = 6,          // 老化
    INSTALL_DAMAGE = 7  // 安装破损
};

// 简化的几何结构
struct SimpleRect {
    int x, y, width, height;
    SimpleRect() : x(0), y(0), width(0), height(0) {}
    SimpleRect(int x_, int y_, int w_, int h_) : x(x_), y(y_), width(w_), height(h_) {}
};

struct SimplePoint {
    float x, y;
    SimplePoint() : x(0), y(0) {}
    SimplePoint(float x_, float y_) : x(x_), y(y_) {}
};

struct SimpleImage {
    int width, height, channels;
    std::vector<unsigned char> data;
    SimpleImage() : width(0), height(0), channels(0) {}
    bool empty() const { return data.empty(); }
};

// 损伤信息结构
struct DamageInfo {
    DamageType type;                    // 损伤类型
    SimpleRect boundingBox;             // 边界框
    std::vector<SimplePoint> contour;   // 轮廓点
    SimplePoint center;                 // 中心点
    double area;                        // 面积(像素)
    double length;                      // 长度(像素，主要用于裂缝)
    double width;                       // 宽度(像素，主要用于裂缝)
    double confidence;                  // 置信度
    std::string description;            // 描述信息
    
    DamageInfo() : type(DamageType::NONE), area(0.0), length(0.0),
                   width(0.0), confidence(0.0) {}
};

// 检测结果结构
struct DetectionResult {
    int cameraId;                           // 摄像头ID
    SimpleImage originalImage;              // 原始图像
    SimpleImage processedImage;             // 处理后图像
    std::vector<DamageInfo> damages;        // 检测到的损伤
    std::chrono::system_clock::time_point timestamp; // 时间戳
    double processingTime;                  // 处理时间(ms)
    bool isValid;                          // 结果是否有效
    
    DetectionResult() : cameraId(-1), processingTime(0.0), isValid(false) {
        timestamp = std::chrono::system_clock::now();
    }
};

// 摄像头状态枚举
enum class CameraStatus {
    DISCONNECTED = 0,   // 未连接
    CONNECTED = 1,      // 已连接
    CAPTURING = 2,      // 正在采集
    ERROR = 3           // 错误状态
};

// 摄像头信息结构
struct CameraInfo {
    int id;                    // 摄像头ID
    std::string devicePath;    // 设备路径
    CameraStatus status;       // 状态
    int width;                 // 图像宽度
    int height;                // 图像高度
    double fps;                // 实际帧率
    std::string errorMsg;      // 错误信息
    
    CameraInfo() : id(-1), status(CameraStatus::DISCONNECTED),
                   width(0), height(0), fps(0.0) {}
};

// 系统状态结构
struct SystemStatus {
    bool isRunning;                        // 系统是否运行
    std::vector<CameraInfo> cameras;       // 摄像头状态
    double avgProcessingTime;              // 平均处理时间
    int totalFramesProcessed;              // 总处理帧数
    int totalDamagesDetected;              // 总检测损伤数
    std::chrono::system_clock::time_point startTime; // 启动时间
    
    SystemStatus() : isRunning(false), avgProcessingTime(0.0),
                     totalFramesProcessed(0), totalDamagesDetected(0) {
        cameras.resize(Config::CAMERA_COUNT);
        startTime = std::chrono::system_clock::now();
    }
};

// 线程安全的队列模板
template<typename T>
class ThreadSafeQueue {
private:
    std::queue<T> queue_;
    mutable std::mutex mutex_;
    std::condition_variable condition_;
    size_t maxSize_;

public:
    ThreadSafeQueue(size_t maxSize = Config::MAX_QUEUE_SIZE) : maxSize_(maxSize) {}
    
    void push(const T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() >= maxSize_) {
            queue_.pop(); // 移除最旧的元素
        }
        queue_.push(item);
        condition_.notify_one();
    }
    
    bool pop(T& item, int timeoutMs = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        if (timeoutMs < 0) {
            condition_.wait(lock, [this] { return !queue_.empty(); });
        } else {
            if (!condition_.wait_for(lock, std::chrono::milliseconds(timeoutMs),
                                   [this] { return !queue_.empty(); })) {
                return false;
            }
        }
        item = queue_.front();
        queue_.pop();
        return true;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        std::queue<T> empty;
        queue_.swap(empty);
    }
};

// 工具函数
namespace Utils {
    // 获取当前时间戳字符串
    std::string getCurrentTimeString();
    
    // 损伤类型转字符串
    std::string damageTypeToString(DamageType type);
    
    // 计算两点间距离
    double calculateDistance(const SimplePoint& p1, const SimplePoint& p2);
    
    // 像素转毫米 (需要根据实际标定参数调整)
    double pixelToMm(double pixels, double pixelSize = 0.1);
    
    // 毫米转像素
    double mmToPixel(double mm, double pixelSize = 0.1);
    
    // 创建输出目录
    bool createDirectory(const std::string& path);
    
    // 保存图像 (简化版本)
    bool saveImage(const SimpleImage& image, const std::string& filename);
    
    // 日志输出
    void logInfo(const std::string& message);
    void logWarning(const std::string& message);
    void logError(const std::string& message);
} // namespace Utils

#endif // COMMON_SIMPLE_H
